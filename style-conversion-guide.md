# 🔄 Style Conversion Guide: Inline to CSS Classes

## Quick Reference for Converting Your Existing Framer Component

### FIND & REPLACE PATTERNS

Use these exact find/replace patterns in your `framer-component-enhanced.tsx`:

#### 1. Main Container
**FIND:**
```jsx
<div style={{
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif',
  maxWidth: '920px',
  margin: '0 auto',
  padding: '20px',
  background: '#ffffff',
  borderRadius: '16px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  boxSizing: 'border-box'
}}>
```

**REPLACE WITH:**
```jsx
<div className="podcast-name-generator">
```

#### 2. Generator Container
**FIND:**
```jsx
<div style={{
  width: '100%',
  boxSizing: 'border-box'
}}>
```

**REPLACE WITH:**
```jsx
<div className="generator-container">
```

#### 3. Header Section
**FIND:**
```jsx
<div style={{
  textAlign: 'center',
  marginBottom: '32px'
}}>
```

**REPLACE WITH:**
```jsx
<div className="header-section">
```

#### 4. Main Title
**FIND:**
```jsx
<h1 style={{
  fontSize: '3rem',
  fontWeight: '800',
  color: '#1a1a1a',
  margin: '0 0 16px 0',
  background: 'linear-gradient(135deg, #6941C7 0%, #8b5cf6 100%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text'
}}>
```

**REPLACE WITH:**
```jsx
<h1 className="main-title">
```

#### 5. Main Subtitle
**FIND:**
```jsx
<h2 style={{
  fontSize: '1.5rem',
  color: '#4a5568',
  margin: '0 0 32px 0',
  fontWeight: '500',
  lineHeight: '1.4'
}}>
```

**REPLACE WITH:**
```jsx
<h2 className="main-subtitle">
```

#### 6. Benefits Section
**FIND:**
```jsx
<div style={{
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '48px',
  margin: '0 0 48px 0'
}}>
```

**REPLACE WITH:**
```jsx
<div className="benefits-section">
```

#### 7. Benefit Item
**FIND:**
```jsx
<div style={{
  display: 'flex',
  alignItems: 'center',
  gap: '12px'
}}>
```

**REPLACE WITH:**
```jsx
<div className="benefit-item">
```

#### 8. Benefit Checkmark
**FIND:**
```jsx
<div style={{
  width: '24px',
  height: '24px',
  backgroundColor: '#6941C7',
  color: 'white',
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '14px',
  fontWeight: 'bold',
  flexShrink: 0
}}>
```

**REPLACE WITH:**
```jsx
<div className="benefit-checkmark">
```

#### 9. Input Field
**FIND:**
```jsx
<textarea
  style={{
    width: '100%',
    padding: '16px 20px',
    fontSize: '1rem',
    border: '2px solid #e1e5e9',
    borderRadius: '12px',
    resize: 'vertical',
    minHeight: '80px',
    fontFamily: 'inherit',
    transition: 'all 0.2s ease',
    boxSizing: 'border-box'
  }}
```

**REPLACE WITH:**
```jsx
<textarea
  className="input-field"
```

#### 10. Generate Button
**FIND:**
```jsx
<button
  style={{
    alignSelf: 'flex-start',
    padding: '14px 28px',
    fontSize: '1rem',
    fontWeight: '600',
    color: 'white',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    border: 'none',
    borderRadius: '12px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    minWidth: '160px'
  }}
```

**REPLACE WITH:**
```jsx
<button
  className="generate-button"
```

#### 11. Results Grid
**FIND:**
```jsx
<div style={{
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gap: '20px'
}}>
```

**REPLACE WITH:**
```jsx
<div className="results-grid">
```

#### 12. Result Card
**FIND:**
```jsx
<div style={{
  background: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '12px',
  padding: '20px',
  transition: 'all 0.2s ease'
}}>
```

**REPLACE WITH:**
```jsx
<div className="result-card">
```

### COMPLETE CONVERSION STEPS:

1. **Open your current `framer-component-enhanced.tsx`**
2. **Use Find & Replace** (Ctrl+H or Cmd+H) for each pattern above
3. **Remove ALL remaining inline styles** - replace with appropriate CSS classes
4. **Add the complete CSS** from `PodcastNameGenerator.css` to Framer's Custom Code
5. **Test the component** - it should now match localhost exactly

### VERIFICATION:
After conversion, your component should have:
- ✅ NO inline styles (except for dynamic ones like `style={props.style}`)
- ✅ ALL elements using CSS classes
- ✅ Exact same structure as localhost version
- ✅ All animations and responsive design working

### COMMON MISTAKES TO AVOID:
- ❌ Leaving some inline styles unconverted
- ❌ Not adding the complete CSS file to Framer
- ❌ Missing CSS class names
- ❌ Forgetting to test on mobile devices

The key is: **Every inline style must become a CSS class** for the component to work exactly like localhost.
