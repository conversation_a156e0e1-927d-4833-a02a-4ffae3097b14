import React, { useState, useEffect, useRef } from 'react';
import { addPropertyControls, ControlType } from 'framer';

// Props interface for Framer
interface PodcastNameGeneratorProps {
  apiKey?: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
}

// Main component - EXACT COPY of localhost structure with Framer compatibility
export default function PodcastNameGenerator(props: PodcastNameGeneratorProps) {
  // State management - EXACT COPY from localhost
  const [input, setInput] = useState('');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [favorites, setFavorites] = useState<any[]>([]);
  const [likedNames, setLikedNames] = useState(new Set<string>());
  const [dislikedNames, setDislikedNames] = useState(new Set<string>());
  const [flyingCards, setFlyingCards] = useState(new Set<string>());
  const [generationCount, setGenerationCount] = useState(0);
  const [usageCount, setUsageCount] = useState(0);
  const [showOnboarding, setShowOnboarding] = useState(true);
  const [showFeedbackHint, setShowFeedbackHint] = useState(false);
  const [domainStatuses, setDomainStatuses] = useState<{[key: string]: string}>({});
  const [pendingReplacements, setPendingReplacements] = useState(new Set<string>());
  
  // Refs for scroll preservation
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef(0);
  const contentHeightRef = useRef(0);

  // Load usage count from localStorage
  useEffect(() => {
    const savedUsage = localStorage.getItem('podcastGeneratorUsage');
    if (savedUsage) {
      setUsageCount(parseInt(savedUsage, 10));
    }
  }, []);

  // Save usage count to localStorage
  const updateUsageCount = () => {
    const newCount = usageCount + 1;
    setUsageCount(newCount);
    localStorage.setItem('podcastGeneratorUsage', newCount.toString());
  };

  // Check if usage limit reached
  const isUsageLimitReached = usageCount >= 100;

  // Preserve scroll position function
  const preserveScrollPosition = () => {
    if (containerRef.current) {
      const container = containerRef.current;
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const containerRect = container.getBoundingClientRect();
      const documentHeight = document.documentElement.scrollHeight;
      
      scrollPositionRef.current = documentHeight - scrollTop;
      contentHeightRef.current = container.scrollHeight;
    }
  };

  // Restore scroll position function
  const restoreScrollPosition = () => {
    setTimeout(() => {
      if (containerRef.current) {
        const container = containerRef.current;
        const newContentHeight = container.scrollHeight;
        const heightDifference = newContentHeight - contentHeightRef.current;
        const newDocumentHeight = document.documentElement.scrollHeight;
        const targetScrollTop = newDocumentHeight - scrollPositionRef.current + heightDifference;
        
        window.scrollTo({
          top: Math.max(0, targetScrollTop),
          behavior: 'auto'
        });
      }
    }, 50);
  };

  // Generate domain name from podcast name
  const generateDomainName = (podcastName: string): string => {
    return podcastName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '')
      .substring(0, 15) + '.com';
  };

  // Simulate domain availability check
  const checkDomainAvailability = async (domain: string): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    return Math.random() > 0.7 ? 'available' : 'taken';
  };

  // Handle domain checking
  const handleDomainCheck = async (podcastName: string) => {
    const domain = generateDomainName(podcastName);
    setDomainStatuses(prev => ({ ...prev, [podcastName]: 'checking' }));
    
    try {
      const status = await checkDomainAvailability(domain);
      setDomainStatuses(prev => ({ ...prev, [podcastName]: status }));
    } catch (error) {
      setDomainStatuses(prev => ({ ...prev, [podcastName]: 'error' }));
    }
  };

  // Copy to clipboard function
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccessMessage(`"${text}" copied to clipboard!`);
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      setError('Failed to copy to clipboard');
      setTimeout(() => setError(''), 3000);
    }
  };

  // Handle like functionality
  const handleLike = (name: any) => {
    if (likedNames.has(name.name) || flyingCards.has(name.name)) return;

    preserveScrollPosition();
    
    setFlyingCards(prev => new Set([...prev, name.name]));
    setLikedNames(prev => new Set([...prev, name.name]));
    
    setTimeout(() => {
      setFavorites(prev => {
        if (!prev.some(fav => fav.name === name.name)) {
          return [...prev, { ...name, domain: generateDomainName(name.name) }];
        }
        return prev;
      });
      
      setFlyingCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(name.name);
        return newSet;
      });
      
      // Generate new suggestion to replace liked one
      generateReplacementSuggestion(name.name);
      restoreScrollPosition();
    }, 700);

    setShowFeedbackHint(false);
  };

  // Handle dislike functionality
  const handleDislike = (name: any) => {
    if (dislikedNames.has(name.name) || pendingReplacements.has(name.name)) return;

    preserveScrollPosition();
    setDislikedNames(prev => new Set([...prev, name.name]));
    setPendingReplacements(prev => new Set([...prev, name.name]));
    
    // Generate replacement immediately
    generateReplacementSuggestion(name.name);
    setShowFeedbackHint(false);
  };

  // Generate replacement suggestion
  const generateReplacementSuggestion = async (replacedName: string) => {
    try {
      const likedNamesArray = Array.from(likedNames);
      const dislikedNamesArray = Array.from(dislikedNames);
      
      const refinementPrompt = `
        Based on user feedback:
        LIKED names: ${likedNamesArray.join(', ')}
        DISLIKED names: ${dislikedNamesArray.join(', ')}
        
        Generate 1 new podcast name for: "${input}"
        
        Learn from the patterns in liked names and avoid patterns in disliked names.
        Return ONLY a JSON object with this structure:
        {"name": "Podcast Name", "description": "Brief description"}
      `;

      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: refinementPrompt,
          apiKey: props.apiKey || ''
        })
      });

      if (!response.ok) throw new Error('Failed to generate replacement');

      const data = await response.json();
      const newSuggestion = data.suggestions?.[0] || data;

      setResults(prev => prev.map(result => 
        result.name === replacedName ? newSuggestion : result
      ));

      setPendingReplacements(prev => {
        const newSet = new Set(prev);
        newSet.delete(replacedName);
        return newSet;
      });

      // Check domain for new suggestion
      if (newSuggestion?.name) {
        handleDomainCheck(newSuggestion.name);
      }

      restoreScrollPosition();
    } catch (error) {
      console.error('Error generating replacement:', error);
      setPendingReplacements(prev => {
        const newSet = new Set(prev);
        newSet.delete(replacedName);
        return newSet;
      });
    }
  };

  // Main generate function
  const generateNames = async () => {
    if (!input.trim() || loading || isUsageLimitReached) return;

    setLoading(true);
    setError('');
    setResults([]);
    setShowOnboarding(false);
    updateUsageCount();

    try {
      const likedNamesArray = Array.from(likedNames);
      const dislikedNamesArray = Array.from(dislikedNames);
      
      let prompt = `Generate 5 creative, catchy podcast names for: "${input.trim()}"`;
      
      if (likedNamesArray.length > 0 || dislikedNamesArray.length > 0) {
        prompt += `\n\nUser feedback for learning:`;
        if (likedNamesArray.length > 0) {
          prompt += `\nLIKED names (use similar style): ${likedNamesArray.join(', ')}`;
        }
        if (dislikedNamesArray.length > 0) {
          prompt += `\nDISLIKED names (avoid similar style): ${dislikedNamesArray.join(', ')}`;
        }
      }

      prompt += `\n\nReturn a JSON object with this structure:
      {
        "suggestions": [
          {"name": "Podcast Name", "description": "Brief description explaining why this name works"},
          ...
        ]
      }`;

      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: prompt,
          apiKey: props.apiKey || ''
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.suggestions && Array.isArray(data.suggestions)) {
        setResults(data.suggestions);
        setGenerationCount(prev => prev + 1);
        
        // Check domains for all suggestions
        data.suggestions.forEach((suggestion: any) => {
          if (suggestion.name) {
            handleDomainCheck(suggestion.name);
          }
        });
        
        // Show feedback hint after first generation
        if (generationCount === 0) {
          setTimeout(() => setShowFeedbackHint(true), 2000);
        }
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error generating names:', error);
      setError('Failed to generate names. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generateNames();
  };

  // Component JSX starts here - using CSS classes like localhost
  return (
    <div 
      ref={containerRef}
      className="podcast-name-generator"
      style={props.style}
    >
      <div className="generator-container">
        {/* Header Section */}
        <div className="header-section">
          <h1 className="main-title">Free Podcast Name Generator</h1>
          <h2 className="main-subtitle">Create the Perfect Name for Your Podcast in Seconds</h2>
          
          {/* Benefits Section */}
          <div className="benefits-section">
            <div className="benefit-item">
              <div className="benefit-checkmark">✓</div>
              <span className="benefit-text">100% Free Forever</span>
            </div>
            <div className="benefit-item">
              <div className="benefit-checkmark">✓</div>
              <span className="benefit-text">No Sign-up Required</span>
            </div>
            <div className="benefit-item">
              <div className="benefit-checkmark">✓</div>
              <span className="benefit-text">Instant Results</span>
            </div>
          </div>
        </div>

        {/* Usage Limit Warning */}
        {isUsageLimitReached && (
          <div className="limit-reached-banner">
            <div className="limit-content">
              <div className="limit-icon">🚫</div>
              <div className="limit-text">
                <h3>Generation Limit Reached</h3>
                <p>You've used all 100 free generations. The tool is now in read-only mode.</p>
                <p className="limit-note">Thank you for using our Podcast Name Generator!</p>
              </div>
            </div>
          </div>
        )}

        {/* Usage Counter */}
        {!isUsageLimitReached && usageCount > 80 && (
          <div className="usage-counter">
            <p className={`usage-text ${usageCount > 90 ? 'usage-warning' : ''}`}>
              Generations used: {usageCount}/100
            </p>
          </div>
        )}

        {/* Favorites Section */}
        {favorites.length > 0 && (
          <div className="favorites-section">
            <div className="favorites-header">
              <h3>🏆 Your Winning Podcast Names</h3>
            </div>
            <p className="favorites-subtitle">
              These are the names you loved! Each one has been carefully selected based on your preferences.
            </p>
            <div className="favorites-grid">
              {favorites.map((favorite, index) => (
                <div key={`${favorite.name}-${index}`} className="favorite-card">
                  <div className="favorite-content">
                    <h4 className="favorite-name">{favorite.name}</h4>
                    <p className="favorite-description">{favorite.description}</p>

                    {/* Domain Info */}
                    <div className="domain-name">
                      <span className="domain-label">Suggested domain:</span>
                      <span className="domain-text">{favorite.domain}</span>
                    </div>
                  </div>
                  <div className="favorite-actions">
                    <button
                      className="copy-button small"
                      onClick={() => copyToClipboard(favorite.name)}
                    >
                      Copy Name
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Onboarding Banner */}
        {showOnboarding && (
          <div className="onboarding-banner">
            <div className="onboarding-content">
              <span className="onboarding-icon">💡</span>
              <div className="onboarding-text">
                <strong>Pro Tip:</strong> After generating names, use the 👍 and 👎 buttons to teach the AI your preferences.
                The more feedback you give, the better the suggestions become!
              </div>
            </div>
          </div>
        )}

        {/* Feedback Hint */}
        {showFeedbackHint && results.length > 0 && (
          <div className="feedback-hint">
            <div className="feedback-hint-content">
              <span className="feedback-hint-icon">👆</span>
              <div>
                <p><strong>Love it or leave it?</strong></p>
                <p>Click 👍 on names you like or 👎 on ones you don't. The AI learns from your choices!</p>
              </div>
            </div>
          </div>
        )}

        {/* Input Section */}
        {results.length === 0 ? (
          <div className="initial-input-section">
            <div className="initial-input-header">
              <h2>Describe Your Podcast</h2>
            </div>
            <p className="initial-input-description">
              Tell us about your podcast topic and get 5 catchy, high-converting name suggestions powered by AI
            </p>

            <form onSubmit={handleSubmit} className="input-form">
              <div className="input-container">
                <textarea
                  className="input-field"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="e.g., A podcast about entrepreneurship, business tips, and success stories from startup founders..."
                  disabled={loading || isUsageLimitReached}
                />

                <div className="button-social-container">
                  <button
                    type="submit"
                    className={`generate-button ${isUsageLimitReached ? 'disabled' : ''}`}
                    disabled={loading || !input.trim() || isUsageLimitReached}
                  >
                    {loading ? 'Generating...' : 'Generate Names'}
                  </button>

                  {/* Social Proof */}
                  <div className="social-proof">
                    <div className="user-avatars">
                      <div className="avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="User" />
                      </div>
                      <div className="avatar">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" alt="User" />
                      </div>
                      <div className="avatar">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" alt="User" />
                      </div>
                    </div>
                    <div className="rating-section">
                      <div className="stars">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className="star">★</span>
                        ))}
                      </div>
                      <span className="trust-text">Trusted by 12k+ Users</span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        ) : (
          <div className="input-section-simple">
            <div className="input-help-message-simple">
              <p className="input-main-description">
                Describe your podcast topic and get 5 catchy, high-converting name suggestions powered by AI
              </p>
              <p className="input-sub-description">
                Your favorites are safely stored above. Feel free to generate more ideas!
              </p>
            </div>

            <form onSubmit={handleSubmit} className="input-form">
              <div className="input-container">
                <textarea
                  className="input-field"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="e.g., A podcast about entrepreneurship, business tips, and success stories from startup founders..."
                  disabled={loading || isUsageLimitReached}
                />

                <div className="button-social-container">
                  <button
                    type="submit"
                    className={`generate-button ${isUsageLimitReached ? 'disabled' : ''}`}
                    disabled={loading || !input.trim() || isUsageLimitReached}
                  >
                    {loading ? 'Generating...' : 'Generate More Names'}
                  </button>

                  {/* Social Proof */}
                  <div className="social-proof">
                    <div className="user-avatars">
                      <div className="avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="User" />
                      </div>
                      <div className="avatar">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" alt="User" />
                      </div>
                      <div className="avatar">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" alt="User" />
                      </div>
                    </div>
                    <div className="rating-section">
                      <div className="stars">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className="star">★</span>
                        ))}
                      </div>
                      <span className="trust-text">Trusted by 12k+ Users</span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {error}
          </div>
        )}

        {/* Success Message */}
        {successMessage && (
          <div className="success-message">
            <span className="success-icon">✅</span>
            {successMessage}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Generating creative podcast names...</p>
          </div>
        )}

        {/* Results Section */}
        {results.length > 0 && (
          <div className="results-container">
            <div className="suggestions-section">
              <div className="suggestions-header">
                <h3>AI-Generated Podcast Name Suggestions</h3>
              </div>
              <p className="suggestions-subtitle">
                Click 👍 to save names you love, or 👎 to get better suggestions. The AI learns from your feedback!
              </p>

              <div className="results-grid">
                {results.map((result, index) => {
                  const isLiked = likedNames.has(result.name);
                  const isDisliked = dislikedNames.has(result.name);
                  const isFlying = flyingCards.has(result.name);
                  const isPending = pendingReplacements.has(result.name);
                  const domainStatus = domainStatuses[result.name];
                  const domain = generateDomainName(result.name);

                  return (
                    <div
                      key={`${result.name}-${index}`}
                      className={`result-card ${isLiked ? 'liked' : ''} ${isDisliked ? 'disliked' : ''} ${isFlying ? 'flying-to-favorites' : ''} ${isPending ? 'pending' : ''}`}
                    >
                      <div className="result-header">
                        <h3 className="result-name">{result.name}</h3>
                        <button
                          className="copy-button"
                          onClick={() => copyToClipboard(result.name)}
                        >
                          Copy
                        </button>
                      </div>

                      <p className="result-description">{result.description}</p>

                      {/* Domain Info */}
                      <div className="domain-info inline">
                        <div className="domain-name">
                          <span className="domain-label">Domain:</span>
                          <span className="domain-text">{domain}</span>
                        </div>
                        <div className={`domain-status ${domainStatus || 'checking'}`}>
                          {domainStatus === 'checking' && (
                            <>
                              <span className="domain-spinner">⏳</span>
                              <span>Checking...</span>
                            </>
                          )}
                          {domainStatus === 'available' && (
                            <>
                              <span className="domain-icon">✅</span>
                              <span>Available</span>
                            </>
                          )}
                          {domainStatus === 'taken' && (
                            <>
                              <span className="domain-icon">❌</span>
                              <span>Taken</span>
                            </>
                          )}
                          {domainStatus === 'error' && (
                            <>
                              <span className="domain-icon">⚠️</span>
                              <span>Check failed</span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="result-actions">
                        <div className="feedback-buttons">
                          <button
                            className={`feedback-button like-button ${isLiked ? 'active' : ''}`}
                            onClick={() => handleLike(result)}
                            disabled={isLiked || isFlying}
                            title="I love this name!"
                          >
                            👍
                          </button>
                          <button
                            className={`feedback-button dislike-button ${isDisliked ? 'active' : ''} ${isPending ? 'loading' : ''}`}
                            onClick={() => handleDislike(result)}
                            disabled={isDisliked || isPending}
                            title="Not quite right"
                          >
                            👎
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Add property controls for Framer
addPropertyControls(PodcastNameGenerator, {
  apiKey: {
    type: ControlType.String,
    title: "API Key",
    description: "Google Gemini API key (optional)",
    defaultValue: ""
  }
});
